/**
 * Main Dashboard page
 */

import React, { useEffect, useState } from 'react';
import {
  Settings,
  Upload,
  Play,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { usePreferencesStore } from '../store/preferencesStore';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { PreferencesPanel } from '../components/dashboard/PreferencesPanel';
import { StatsCards } from '../components/dashboard/StatsCards';
import { FileUpload } from '../components/dashboard/FileUpload';
import toast from 'react-hot-toast';

export const Dashboard: React.FC = () => {
  const { loadPreferences, loadAnalysisTypes } = usePreferencesStore();
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [documentIds, setDocumentIds] = useState<string[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  useEffect(() => {
    // Load initial data
    loadPreferences();
    loadAnalysisTypes();

    // Check for existing files in sessionStorage on component mount
    checkStoredFiles();
  }, [loadPreferences, loadAnalysisTypes]);

  // Listen for storage changes to update file count
  useEffect(() => {
    const handleStorageChange = () => {
      checkStoredFiles();
    };

    // Listen for custom events when files are added/removed
    window.addEventListener('casebuilder-files-changed', handleStorageChange);

    // Also check periodically in case we miss events
    const interval = setInterval(checkStoredFiles, 1000);

    return () => {
      window.removeEventListener('casebuilder-files-changed', handleStorageChange);
      clearInterval(interval);
    };
  }, []);

  const checkStoredFiles = () => {
    const storedFiles = JSON.parse(sessionStorage.getItem('casebuilder-files') || '[]');
    if (storedFiles.length > 0) {
      // Update state to reflect stored files
      setDocumentIds(storedFiles.map((f: any) => f.id));
      // Create mock File objects for display purposes
      const mockFiles = storedFiles.map((f: any) => ({
        name: f.name,
        size: f.size,
        type: f.type,
        lastModified: f.lastModified
      }));
      setUploadedFiles(mockFiles);
    }
  };

  const handleFilesUploaded = (files: File[], docIds: string[]) => {
    setUploadedFiles(prev => [...prev, ...files]);
    setDocumentIds(prev => [...prev, ...docIds]);
  };

  const handleStartAnalysis = async () => {
    if (documentIds.length === 0) {
      toast.error('Please upload documents before starting analysis');
      return;
    }

    setIsAnalyzing(true);

    try {
      // Get files from sessionStorage
      const storedFiles = JSON.parse(sessionStorage.getItem('casebuilder-files') || '[]');

      if (storedFiles.length === 0) {
        toast.error('No files found in memory. Please upload documents first.');
        return;
      }

      // Get current preferences
      const { preferences } = usePreferencesStore.getState();

      // Show progress toast
      const progressToast = toast.loading('Starting Police Report Summary analysis...');

      // Simulate analysis process
      await simulatePoliceReportAnalysis(storedFiles, preferences, progressToast);

    } catch (error) {
      console.error('Analysis failed:', error);
      toast.error('Analysis failed. Please try again.');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const simulatePoliceReportAnalysis = async (files: any[], preferences: any, progressToast: string) => {
    try {
      // Step 1: Validate files
      toast.loading('Validating uploaded documents...', { id: progressToast });
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Step 2: Extract text content
      toast.loading('Extracting text from documents...', { id: progressToast });
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Step 3: Analyze content
      toast.loading('Analyzing police report content...', { id: progressToast });
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Step 4: Generate summary
      toast.loading('Generating Police Report Summary...', { id: progressToast });
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Generate mock analysis result
      const analysisResult = generateMockPoliceReportSummary(files, preferences);

      // Store result in sessionStorage
      const analysisId = `analysis_${Date.now()}`;
      const analysisData = {
        id: analysisId,
        type: 'police_report_summary',
        status: 'completed',
        result: analysisResult,
        files: files.map(f => ({ id: f.id, name: f.name })),
        preferences: preferences,
        created_at: new Date().toISOString(),
        completed_at: new Date().toISOString()
      };

      // Store analysis result
      const existingAnalyses = JSON.parse(sessionStorage.getItem('casebuilder-analyses') || '[]');
      existingAnalyses.push(analysisData);
      sessionStorage.setItem('casebuilder-analyses', JSON.stringify(existingAnalyses));

      // Success
      toast.success('Police Report Summary completed successfully!', { id: progressToast });

      // Show result in a modal or new page (for now, just log it)
      console.log('Analysis Result:', analysisResult);

      // Show a preview of the result
      setTimeout(() => {
        alert(`Analysis completed!\n\nPreview:\n${analysisResult.substring(0, 200)}...\n\nFull result saved to session.`);
      }, 500);

    } catch (error) {
      toast.error('Analysis failed during processing', { id: progressToast });
      throw error;
    }
  };

  const generateMockPoliceReportSummary = (files: any[], preferences: any) => {
    const currentDate = new Date().toLocaleDateString();
    const fileNames = files.map(f => f.name).join(', ');

    return `# POLICE REPORT SUMMARY

**Generated on:** ${currentDate}
**Documents Analyzed:** ${fileNames}
**Analysis Type:** Police Report Summary
**Detail Level:** ${preferences?.analysis_detail_level || 'Standard'}

## INCIDENT OVERVIEW

**Date of Incident:** [Date extracted from police report]
**Time of Incident:** [Time extracted from police report]
**Location:** [Location details from report]
**Report Number:** [Police report number]
**Reporting Officer:** [Officer name and badge number]

## PARTIES INVOLVED

### Primary Party
- **Name:** [Name from report]
- **Role:** [Driver/Pedestrian/Passenger]
- **Vehicle:** [Vehicle description if applicable]
- **Insurance:** [Insurance information if available]

### Secondary Party
- **Name:** [Name from report]
- **Role:** [Driver/Pedestrian/Passenger]
- **Vehicle:** [Vehicle description if applicable]
- **Insurance:** [Insurance information if available]

## INCIDENT DESCRIPTION

Based on the police report analysis, the incident occurred when [detailed description of the sequence of events leading to the incident]. The officer's investigation revealed [key findings from the report].

**Weather Conditions:** [Weather at time of incident]
**Road Conditions:** [Road surface and visibility conditions]
**Traffic Control:** [Traffic signals, signs, or other controls present]

## WITNESS STATEMENTS

The following witnesses were identified and provided statements:
1. [Witness name and contact information]
2. [Additional witnesses as documented]

## OFFICER'S FINDINGS

The investigating officer determined that [officer's conclusions about fault, violations, or contributing factors]. Citations were issued for [any traffic violations noted].

## EVIDENCE COLLECTED

- [List of physical evidence]
- [Photographs taken]
- [Measurements or diagrams]
- [Other documentation]

## LIABILITY ASSESSMENT

**Primary Contributing Factors:**
- [Factor 1 with explanation]
- [Factor 2 with explanation]
- [Additional factors as identified]

**Potential Liability Distribution:**
- Party 1: [Percentage and reasoning]
- Party 2: [Percentage and reasoning]

## RECOMMENDATIONS

Based on this analysis, the following actions are recommended:
1. [Recommendation 1]
2. [Recommendation 2]
3. [Additional recommendations]

---
*This summary was generated using CaseBuilder AI analysis tools. Please review all source documents for complete accuracy.*`;
  };

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Configure your preferences and upload documents for analysis
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <StatsCards />

      {/* Document Upload - Full Width */}
      <Card>
        <Card.Header>
          <div className="flex items-center">
            <Upload className="w-5 h-5 text-primary-400 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">
              Upload Documents
            </h2>
          </div>
          <p className="text-sm text-gray-600 mt-1">
            Upload PDFs, images, and documents for analysis
          </p>
        </Card.Header>
        <Card.Body>
          <FileUpload onFilesUploaded={handleFilesUploaded} />
        </Card.Body>
      </Card>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Preferences Panel - Takes 2 columns on large screens */}
        <div className="lg:col-span-2">
          <Card>
            <Card.Header>
              <div className="flex items-center">
                <Settings className="w-5 h-5 text-primary-400 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">
                  Analysis Preferences
                </h2>
              </div>
              <p className="text-sm text-gray-600 mt-1">
                Configure your analysis settings and document processing preferences
              </p>
            </Card.Header>
            <Card.Body>
              <PreferencesPanel />
            </Card.Body>
          </Card>
        </div>

        {/* Workflow Panel - Takes 1 column */}
        <div className="space-y-6">

          {/* Generate Button */}
          <Card>
            <Card.Header>
              <div className="flex items-center">
                <Play className="w-5 h-5 text-success-500 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">
                  Generate Analysis
                </h3>
              </div>
            </Card.Header>
            <Card.Body>
              <div className="text-center py-4">
                <p className="text-gray-600 mb-4 text-sm">
                  {uploadedFiles.length === 0
                    ? 'Upload documents and configure preferences to enable generation'
                    : `${uploadedFiles.length} document(s) ready for analysis`
                  }
                </p>
                <Button
                  variant="success"
                  size="lg"
                  className="w-full"
                  disabled={uploadedFiles.length === 0 || isAnalyzing}
                  onClick={handleStartAnalysis}
                >
                  <Play className="w-4 h-4 mr-2" />
                  {isAnalyzing ? 'Starting Analysis...' : 'Generate Analysis'}
                </Button>
                {uploadedFiles.length === 0 && (
                  <p className="text-xs text-gray-500 mt-2">
                    Upload documents to enable this feature
                  </p>
                )}
              </div>
            </Card.Body>
          </Card>

          {/* Quick Actions */}
          <Card>
            <Card.Header>
              <h3 className="text-lg font-semibold text-gray-900">
                Quick Actions
              </h3>
            </Card.Header>
            <Card.Body>
              <div className="space-y-3">
                <Button variant="secondary" size="sm" className="w-full justify-start">
                  <Clock className="w-4 h-4 mr-2" />
                  View Recent Sessions
                </Button>
                <Button variant="secondary" size="sm" className="w-full justify-start">
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Analysis History
                </Button>
                <Button variant="secondary" size="sm" className="w-full justify-start">
                  <AlertCircle className="w-4 h-4 mr-2" />
                  Help & Support
                </Button>
              </div>
            </Card.Body>
          </Card>
        </div>
      </div>
    </div>
  );
};
