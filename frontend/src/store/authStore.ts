/**
 * Authentication store using Zustand
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { AuthStore, LoginRequest, User } from '../types/auth.types';
import { authService } from '../services/auth.service';
import toast from 'react-hot-toast';

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      token: null,
      sessionId: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials: LoginRequest) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await authService.login(credentials);

          set({
            user: response.user,
            token: response.access_token,
            sessionId: response.user.session_id,
            isAuthenticated: true,
            isLoading: false,
            error: null
          });

          toast.success(`Welcome back, ${response.user.first_name}!`);
        } catch (error: any) {
          const errorMessage = error.response?.data?.detail || 'Login failed';
          
          set({
            user: null,
            token: null,
            sessionId: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage
          });
          
          toast.error(errorMessage);
          throw error;
        }
      },

      logout: async () => {
        set({ isLoading: true });

        try {
          await authService.logout();

          set({
            user: null,
            token: null,
            sessionId: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          });

          toast.success('Logged out successfully');
        } catch (error: any) {
          // Even if logout fails on server, clear local state
          set({
            user: null,
            token: null,
            sessionId: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          });

          toast.success('Logged out');
        }
      },

      refreshToken: async () => {
        const { token } = get();
        
        if (!token) {
          throw new Error('No token available');
        }
        
        try {
          const response = await authService.refreshToken();
          
          set({
            token: response.access_token,
            user: response.user,
            error: null
          });
        } catch (error: any) {
          // If refresh fails, logout user
          set({
            user: null,
            token: null,
            sessionId: null,
            isAuthenticated: false,
            error: 'Session expired'
          });
          
          toast.error('Session expired. Please login again.');
          throw error;
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      }
    }),
    {
      name: 'casebuilder-auth',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        sessionId: state.sessionId,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
);
