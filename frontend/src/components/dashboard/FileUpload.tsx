import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, X, AlertCircle, CheckCircle } from 'lucide-react';
import { Card } from '../ui/Card';
import { documentAPI, handleApiError } from '../../services/apiService';

interface UploadedFile {
  id: string;
  file: File;
  status: 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
  documentId?: string;
}

interface FileUploadProps {
  onFilesUploaded?: (files: File[], documentIds: string[]) => void;
  maxFiles?: number;
  maxSize?: number; // in bytes
  acceptedTypes?: string[];
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onFilesUploaded,
  maxFiles = 10,
  maxSize = 50 * 1024 * 1024, // 50MB
  acceptedTypes = ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx']
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);

  const uploadToBackend = useCallback(async (uploadedFile: UploadedFile) => {
    try {
      // Update progress to show uploading
      setUploadedFiles(prev =>
        prev.map(f =>
          f.id === uploadedFile.id
            ? { ...f, progress: 10 }
            : f
        )
      );

      // Upload to backend
      const response = await documentAPI.upload(uploadedFile.file);
      
      // Update progress to show success
      setUploadedFiles(prev =>
        prev.map(f =>
          f.id === uploadedFile.id
            ? { 
                ...f, 
                status: 'success', 
                progress: 100,
                documentId: response.document_id
              }
            : f
        )
      );

      // Notify parent component with successful uploads
      const allFiles = uploadedFiles.concat([{
        ...uploadedFile,
        status: 'success',
        documentId: response.document_id
      }]);
      
      const successfulFiles = allFiles.filter(f => f.status === 'success');
      const documentIds = successfulFiles
        .map(f => f.documentId)
        .filter(id => id !== undefined) as string[];
      
      if (onFilesUploaded && documentIds.length > 0) {
        onFilesUploaded(
          successfulFiles.map(f => f.file),
          documentIds
        );
      }

    } catch (error) {
      console.error('Upload failed:', error);
      const errorMessage = handleApiError(error);
      
      setUploadedFiles(prev =>
        prev.map(f =>
          f.id === uploadedFile.id
            ? { 
                ...f, 
                status: 'error', 
                progress: 0,
                error: errorMessage
              }
            : f
        )
      );
    }
  }, [uploadedFiles, onFilesUploaded]);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles: UploadedFile[] = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      status: 'uploading',
      progress: 0
    }));

    setUploadedFiles(prev => [...prev, ...newFiles]);

    // Upload files to backend
    newFiles.forEach(uploadedFile => {
      uploadToBackend(uploadedFile);
    });
  }, [uploadToBackend]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'image/*': ['.jpg', '.jpeg', '.png'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
    },
    maxFiles,
    maxSize,
    multiple: true
  });

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <Card>
        <Card.Body>
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors duration-200 ${
              isDragActive
                ? 'border-primary-400 bg-primary-50'
                : 'border-gray-300 hover:border-primary-400 hover:bg-gray-50'
            }`}
          >
            <input {...getInputProps()} />
            <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Upload Documents
            </h3>
            <p className="text-gray-600 mb-4">
              Drag and drop files here, or click to select files
            </p>
            <div className="text-sm text-gray-500">
              <p>Supported formats: .pdf, .jpg, .png, .doc, .docx</p>
              <p>Maximum size: 50 MB</p>
              <p>Maximum files: 10</p>
            </div>
          </div>
        </Card.Body>
      </Card>

      {/* Uploaded Files List */}
      {uploadedFiles.length > 0 && (
        <Card>
          <Card.Header>
            <h3 className="text-lg font-semibold text-gray-900">
              Uploaded Files ({uploadedFiles.length})
            </h3>
          </Card.Header>
          <Card.Body>
            <div className="space-y-3">
              {uploadedFiles.map((uploadedFile) => (
                <div key={uploadedFile.id} className="space-y-2">
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <FileText className="w-5 h-5 text-gray-500" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {uploadedFile.file.name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatFileSize(uploadedFile.file.size)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {uploadedFile.status === 'uploading' && (
                        <div className="w-5 h-5 border-2 border-primary-500 border-t-transparent rounded-full animate-spin" />
                      )}
                      {uploadedFile.status === 'success' && (
                        <CheckCircle className="w-5 h-5 text-success-500" />
                      )}
                      {uploadedFile.status === 'error' && (
                        <AlertCircle className="w-5 h-5 text-error-500" />
                      )}
                      <button
                        onClick={() => removeFile(uploadedFile.id)}
                        className="p-1 hover:bg-gray-200 rounded transition-colors duration-200"
                      >
                        <X className="w-4 h-4 text-gray-400" />
                      </button>
                    </div>
                  </div>
                  
                  {/* Show error message for this specific file */}
                  {uploadedFile.status === 'error' && uploadedFile.error && (
                    <div className="ml-8 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-start">
                        <AlertCircle className="w-4 h-4 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                        <div className="text-sm text-red-700">
                          <span className="font-medium">Error:</span> {uploadedFile.error}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </Card.Body>
        </Card>
      )}
    </div>
  );
};
