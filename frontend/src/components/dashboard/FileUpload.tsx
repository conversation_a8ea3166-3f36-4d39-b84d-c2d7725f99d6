import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, X, AlertCircle, CheckCircle } from 'lucide-react';
import { Card } from '../ui/Card';
import { documentAPI, handleApiError } from '../../services/apiService';

interface UploadedFile {
  id: string;
  file: File;
  status: 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
  documentId?: string;
}

interface FileUploadProps {
  onFilesUploaded?: (files: File[], documentIds: string[]) => void;
  maxFiles?: number;
  maxSize?: number; // in bytes
  acceptedTypes?: string[];
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onFilesUploaded,
  maxFiles = 10,
  maxSize = 50 * 1024 * 1024, // 50MB
  acceptedTypes = ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx']
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);

  const uploadToBackend = useCallback(async (uploadedFile: UploadedFile) => {
    try {
      // Update progress to show uploading
      setUploadedFiles(prev =>
        prev.map(f =>
          f.id === uploadedFile.id
            ? { ...f, progress: 10 }
            : f
        )
      );

      // Upload to backend
      const response = await documentAPI.upload(uploadedFile.file);
      
      // Update progress to show success
      setUploadedFiles(prev =>
        prev.map(f =>
          f.id === uploadedFile.id
            ? { 
                ...f, 
                status: 'success', 
                progress: 100,
                documentId: response.document_id
              }
            : f
        )
      );

      // Notify parent component with successful uploads
      const allFiles = uploadedFiles.concat([{
        ...uploadedFile,
        status: 'success',
        documentId: response.document_id
      }]);
      
      const successfulFiles = allFiles.filter(f => f.status === 'success');
      const documentIds = successfulFiles
        .map(f => f.documentId)
        .filter(id => id !== undefined) as string[];
      
      if (onFilesUploaded && documentIds.length > 0) {
        onFilesUploaded(
          successfulFiles.map(f => f.file),
          documentIds
        );
      }

    } catch (error) {
      console.error('Upload failed:', error);
      const errorMessage = handleApiError(error);
      
      setUploadedFiles(prev =>
        prev.map(f =>
          f.id === uploadedFile.id
            ? { 
                ...f, 
                status: 'error', 
                progress: 0,
                error: errorMessage
              }
            : f
        )
      );
    }
  }, [uploadedFiles, onFilesUploaded]);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles: UploadedFile[] = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      status: 'uploading',
      progress: 0
    }));

    setUploadedFiles(prev => [...prev, ...newFiles]);

    // Upload files to backend
    newFiles.forEach(uploadedFile => {
      uploadToBackend(uploadedFile);
    });
  }, [uploadToBackend]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'image/*': ['.jpg', '.jpeg', '.png'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
    },
    maxFiles,
    maxSize,
    multiple: true
  });

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      <Card>
        <Card.Body>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left side - Upload Zone */}
            <div>
              <div className="flex items-center mb-4">
                <Upload className="w-5 h-5 text-primary-500 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">Upload Documents</h3>
              </div>
              <div
                {...getRootProps()}
                className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all duration-200 ${
                  isDragActive
                    ? 'border-primary-400 bg-primary-50 scale-105'
                    : 'border-gray-300 hover:border-primary-400 hover:bg-gray-50'
                }`}
              >
                <input {...getInputProps()} />
                <Upload className="w-8 h-8 text-gray-400 mx-auto mb-3" />
                <p className="text-gray-600 mb-2 font-medium">
                  {isDragActive ? 'Drop files here' : 'Drag & drop files here'}
                </p>
                <p className="text-sm text-gray-500 mb-3">or click to browse</p>
                <div className="text-xs text-gray-400 space-y-1">
                  <p>PDF, JPG, PNG, DOC, DOCX</p>
                  <p>Max: 50MB per file • Up to 10 files</p>
                </div>
              </div>
            </div>

            {/* Right side - File List */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  Files ({uploadedFiles.length})
                </h3>
                {uploadedFiles.length > 0 && (
                  <button
                    onClick={() => setUploadedFiles([])}
                    className="text-sm text-gray-500 hover:text-red-500 transition-colors"
                  >
                    Clear all
                  </button>
                )}
              </div>

              <div className="space-y-2 max-h-80 overflow-y-auto">
                {uploadedFiles.length === 0 ? (
                  <div className="text-center py-8 text-gray-400">
                    <FileText className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No files uploaded yet</p>
                  </div>
                ) : (
                  uploadedFiles.map((uploadedFile) => (
                    <div key={uploadedFile.id} className="space-y-2">
                      <div className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg hover:shadow-sm transition-shadow">
                        <div className="flex items-center space-x-3 flex-1 min-w-0">
                          <div className="flex-shrink-0">
                            {uploadedFile.status === 'uploading' && (
                              <div className="w-5 h-5 border-2 border-primary-500 border-t-transparent rounded-full animate-spin" />
                            )}
                            {uploadedFile.status === 'success' && (
                              <CheckCircle className="w-5 h-5 text-green-500" />
                            )}
                            {uploadedFile.status === 'error' && (
                              <AlertCircle className="w-5 h-5 text-red-500" />
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {uploadedFile.file.name}
                            </p>
                            <div className="flex items-center space-x-2 text-xs text-gray-500">
                              <span>{formatFileSize(uploadedFile.file.size)}</span>
                              <span>•</span>
                              <span className={`font-medium ${
                                uploadedFile.status === 'success' ? 'text-green-600' :
                                uploadedFile.status === 'error' ? 'text-red-600' :
                                'text-blue-600'
                              }`}>
                                {uploadedFile.status === 'uploading' ? 'Uploading...' :
                                 uploadedFile.status === 'success' ? 'Uploaded' :
                                 'Failed'}
                              </span>
                            </div>
                          </div>
                        </div>
                        <button
                          onClick={() => removeFile(uploadedFile.id)}
                          className="flex-shrink-0 p-1 hover:bg-gray-100 rounded transition-colors duration-200"
                        >
                          <X className="w-4 h-4 text-gray-400 hover:text-red-500" />
                        </button>
                      </div>

                      {/* Progress bar for uploading files */}
                      {uploadedFile.status === 'uploading' && (
                        <div className="ml-8 mr-8">
                          <div className="w-full bg-gray-200 rounded-full h-1">
                            <div
                              className="bg-primary-500 h-1 rounded-full transition-all duration-300"
                              style={{ width: `${uploadedFile.progress}%` }}
                            />
                          </div>
                        </div>
                      )}

                      {/* Error message */}
                      {uploadedFile.status === 'error' && uploadedFile.error && (
                        <div className="ml-8 mr-8 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                          <span className="font-medium">Error:</span> {uploadedFile.error}
                        </div>
                      )}
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </Card.Body>
      </Card>
    </div>
  );
};
