import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, X, AlertCircle, CheckCircle } from 'lucide-react';
import { Card } from '../ui/Card';
import { documentAPI, handleApiError } from '../../services/apiService';

interface UploadedFile {
  id: string;
  file: File;
  status: 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
  documentId?: string;
}

interface FileUploadProps {
  onFilesUploaded?: (files: File[], documentIds: string[]) => void;
  maxFiles?: number;
  maxSize?: number; // in bytes
  acceptedTypes?: string[];
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onFilesUploaded,
  maxFiles = 10,
  maxSize = 50 * 1024 * 1024, // 50MB
  acceptedTypes = ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx']
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);

  const uploadToBackend = useCallback(async (uploadedFile: UploadedFile) => {
    try {
      // Update progress to show uploading
      setUploadedFiles(prev =>
        prev.map(f =>
          f.id === uploadedFile.id
            ? { ...f, progress: 10 }
            : f
        )
      );

      // Upload to backend
      const response = await documentAPI.upload(uploadedFile.file);
      
      // Update progress to show success
      setUploadedFiles(prev =>
        prev.map(f =>
          f.id === uploadedFile.id
            ? { 
                ...f, 
                status: 'success', 
                progress: 100,
                documentId: response.document_id
              }
            : f
        )
      );

      // Notify parent component with successful uploads
      const allFiles = uploadedFiles.concat([{
        ...uploadedFile,
        status: 'success',
        documentId: response.document_id
      }]);
      
      const successfulFiles = allFiles.filter(f => f.status === 'success');
      const documentIds = successfulFiles
        .map(f => f.documentId)
        .filter(id => id !== undefined) as string[];
      
      if (onFilesUploaded && documentIds.length > 0) {
        onFilesUploaded(
          successfulFiles.map(f => f.file),
          documentIds
        );
      }

    } catch (error) {
      console.error('Upload failed:', error);
      const errorMessage = handleApiError(error);
      
      setUploadedFiles(prev =>
        prev.map(f =>
          f.id === uploadedFile.id
            ? { 
                ...f, 
                status: 'error', 
                progress: 0,
                error: errorMessage
              }
            : f
        )
      );
    }
  }, [uploadedFiles, onFilesUploaded]);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles: UploadedFile[] = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      status: 'uploading',
      progress: 0
    }));

    setUploadedFiles(prev => [...prev, ...newFiles]);

    // Upload files to backend
    newFiles.forEach(uploadedFile => {
      uploadToBackend(uploadedFile);
    });
  }, [uploadToBackend]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'image/*': ['.jpg', '.jpeg', '.png'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
    },
    maxFiles,
    maxSize,
    multiple: true
  });

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Upload className="w-5 h-5 text-primary-500 mr-2" />
          <h3 className="text-lg font-semibold text-gray-900">Upload Documents</h3>
        </div>
        {uploadedFiles.length > 0 && (
          <button
            onClick={() => setUploadedFiles([])}
            className="text-sm text-gray-500 hover:text-red-500 transition-colors"
          >
            Clear all ({uploadedFiles.length})
          </button>
        )}
      </div>

      {/* Upload Zone */}
      <Card>
        <Card.Body>
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200 ${
              isDragActive
                ? 'border-primary-400 bg-primary-50 border-solid'
                : 'border-gray-300 hover:border-primary-400 hover:bg-gray-50'
            }`}
          >
            <input {...getInputProps()} />
            <div className="flex flex-col items-center">
              <Upload className={`w-12 h-12 mb-4 ${isDragActive ? 'text-primary-500' : 'text-gray-400'}`} />
              <h4 className="text-lg font-medium text-gray-900 mb-2">
                {isDragActive ? 'Drop your files here' : 'Choose files or drag and drop'}
              </h4>
              <p className="text-gray-600 mb-4">
                Upload PDFs, images, and documents for analysis
              </p>
              <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500">
                <span className="bg-gray-100 px-3 py-1 rounded-full">PDF</span>
                <span className="bg-gray-100 px-3 py-1 rounded-full">JPG</span>
                <span className="bg-gray-100 px-3 py-1 rounded-full">PNG</span>
                <span className="bg-gray-100 px-3 py-1 rounded-full">DOC</span>
                <span className="bg-gray-100 px-3 py-1 rounded-full">DOCX</span>
              </div>
              <p className="text-xs text-gray-400 mt-3">
                Maximum file size: 50MB • Maximum files: 10
              </p>
            </div>
          </div>
        </Card.Body>
      </Card>

      {/* Files List */}
      {uploadedFiles.length > 0 && (
        <Card>
          <Card.Header>
            <h4 className="text-md font-semibold text-gray-900">
              Uploaded Files ({uploadedFiles.length})
            </h4>
          </Card.Header>
          <Card.Body>
            <div className="space-y-3">
              {uploadedFiles.map((uploadedFile) => (
                <div key={uploadedFile.id}>
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <div className="flex items-center space-x-4 flex-1 min-w-0">
                      {/* Status Icon */}
                      <div className="flex-shrink-0">
                        {uploadedFile.status === 'uploading' && (
                          <div className="w-6 h-6 border-2 border-primary-500 border-t-transparent rounded-full animate-spin" />
                        )}
                        {uploadedFile.status === 'success' && (
                          <CheckCircle className="w-6 h-6 text-green-500" />
                        )}
                        {uploadedFile.status === 'error' && (
                          <AlertCircle className="w-6 h-6 text-red-500" />
                        )}
                      </div>

                      {/* File Info */}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {uploadedFile.file.name}
                        </p>
                        <div className="flex items-center space-x-3 mt-1">
                          <span className="text-xs text-gray-500">
                            {formatFileSize(uploadedFile.file.size)}
                          </span>
                          <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                            uploadedFile.status === 'success'
                              ? 'bg-green-100 text-green-700'
                              : uploadedFile.status === 'error'
                              ? 'bg-red-100 text-red-700'
                              : 'bg-blue-100 text-blue-700'
                          }`}>
                            {uploadedFile.status === 'uploading' ? 'Uploading...' :
                             uploadedFile.status === 'success' ? 'Uploaded Successfully' :
                             'Upload Failed'}
                          </span>
                        </div>
                      </div>

                      {/* Remove Button */}
                      <button
                        onClick={() => removeFile(uploadedFile.id)}
                        className="flex-shrink-0 p-2 hover:bg-gray-200 rounded-lg transition-colors duration-200"
                        title="Remove file"
                      >
                        <X className="w-4 h-4 text-gray-400 hover:text-red-500" />
                      </button>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  {uploadedFile.status === 'uploading' && (
                    <div className="mt-2">
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-primary-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${uploadedFile.progress}%` }}
                        />
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        Uploading... {uploadedFile.progress}%
                      </p>
                    </div>
                  )}

                  {/* Error Message */}
                  {uploadedFile.status === 'error' && uploadedFile.error && (
                    <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-start">
                        <AlertCircle className="w-4 h-4 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                        <div className="text-sm text-red-700">
                          <span className="font-medium">Upload Error:</span> {uploadedFile.error}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </Card.Body>
        </Card>
      )}
    </div>
  );
};
