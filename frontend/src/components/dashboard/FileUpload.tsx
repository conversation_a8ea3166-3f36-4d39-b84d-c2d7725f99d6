import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, X, AlertCircle, CheckCircle } from 'lucide-react';
import { documentAPI, handleApiError } from '../../services/apiService';

interface UploadedFile {
  id: string;
  file: File;
  status: 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
  documentId?: string;
}

interface FileUploadProps {
  onFilesUploaded?: (files: File[], documentIds: string[]) => void;
  maxFiles?: number;
  maxSize?: number; // in bytes
  acceptedTypes?: string[];
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onFilesUploaded,
  maxFiles = 10,
  maxSize = 50 * 1024 * 1024, // 50MB
  acceptedTypes = ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx']
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);

  const uploadToBackend = useCallback(async (uploadedFile: UploadedFile) => {
    try {
      // Update progress to show uploading
      setUploadedFiles(prev =>
        prev.map(f =>
          f.id === uploadedFile.id
            ? { ...f, progress: 10 }
            : f
        )
      );

      // Upload to backend
      const response = await documentAPI.upload(uploadedFile.file);
      
      // Update progress to show success
      setUploadedFiles(prev =>
        prev.map(f =>
          f.id === uploadedFile.id
            ? { 
                ...f, 
                status: 'success', 
                progress: 100,
                documentId: response.document_id
              }
            : f
        )
      );

      // Notify parent component with successful uploads
      const allFiles = uploadedFiles.concat([{
        ...uploadedFile,
        status: 'success',
        documentId: response.document_id
      }]);
      
      const successfulFiles = allFiles.filter(f => f.status === 'success');
      const documentIds = successfulFiles
        .map(f => f.documentId)
        .filter(id => id !== undefined) as string[];
      
      if (onFilesUploaded && documentIds.length > 0) {
        onFilesUploaded(
          successfulFiles.map(f => f.file),
          documentIds
        );
      }

    } catch (error) {
      console.error('Upload failed:', error);
      const errorMessage = handleApiError(error);
      
      setUploadedFiles(prev =>
        prev.map(f =>
          f.id === uploadedFile.id
            ? { 
                ...f, 
                status: 'error', 
                progress: 0,
                error: errorMessage
              }
            : f
        )
      );
    }
  }, [uploadedFiles, onFilesUploaded]);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles: UploadedFile[] = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substring(2, 11),
      file,
      status: 'uploading',
      progress: 0
    }));

    setUploadedFiles(prev => [...prev, ...newFiles]);

    // Upload files to backend
    newFiles.forEach(uploadedFile => {
      uploadToBackend(uploadedFile);
    });
  }, [uploadToBackend]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'image/*': ['.jpg', '.jpeg', '.png'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
    },
    maxFiles,
    maxSize,
    multiple: true
  });

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-4">
      {/* Upload Zone - Compact */}
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all duration-200 ${
          isDragActive
            ? 'border-primary-400 bg-primary-50'
            : 'border-gray-300 hover:border-primary-400 hover:bg-gray-50'
        }`}
      >
        <input {...getInputProps()} />
        <div className="flex items-center justify-center space-x-4">
          <Upload className={`w-8 h-8 ${isDragActive ? 'text-primary-500' : 'text-gray-400'}`} />
          <div className="text-left">
            <p className="text-lg font-medium text-gray-900">
              {isDragActive ? 'Drop files here' : 'Upload Documents'}
            </p>
            <p className="text-sm text-gray-600">
              Drag & drop files or click to browse • PDF, JPG, PNG, DOC, DOCX • Max 50MB
            </p>
          </div>
          {uploadedFiles.length > 0 && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                setUploadedFiles([]);
              }}
              className="ml-auto px-3 py-1 text-sm text-red-600 hover:bg-red-50 rounded transition-colors"
            >
              Clear All
            </button>
          )}
        </div>
      </div>

      {/* Files Grid - Horizontal Layout */}
      {uploadedFiles.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
          {uploadedFiles.map((uploadedFile) => (
            <div key={uploadedFile.id} className="relative">
              <div className="bg-white border border-gray-200 rounded-lg p-3 hover:shadow-sm transition-shadow">
                {/* File Header */}
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate" title={uploadedFile.file.name}>
                      {uploadedFile.file.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(uploadedFile.file.size)}
                    </p>
                  </div>
                  <button
                    onClick={() => removeFile(uploadedFile.id)}
                    className="flex-shrink-0 p-1 hover:bg-gray-100 rounded transition-colors"
                  >
                    <X className="w-3 h-3 text-gray-400 hover:text-red-500" />
                  </button>
                </div>

                {/* Status */}
                <div className="flex items-center space-x-2">
                  {uploadedFile.status === 'uploading' && (
                    <>
                      <div className="w-4 h-4 border-2 border-primary-500 border-t-transparent rounded-full animate-spin" />
                      <span className="text-xs text-blue-600">Uploading...</span>
                    </>
                  )}
                  {uploadedFile.status === 'success' && (
                    <>
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-xs text-green-600">Uploaded</span>
                    </>
                  )}
                  {uploadedFile.status === 'error' && (
                    <>
                      <AlertCircle className="w-4 h-4 text-red-500" />
                      <span className="text-xs text-red-600">Failed</span>
                    </>
                  )}
                </div>

                {/* Progress Bar */}
                {uploadedFile.status === 'uploading' && (
                  <div className="mt-2">
                    <div className="w-full bg-gray-200 rounded-full h-1">
                      <div
                        className="bg-primary-500 h-1 rounded-full transition-all duration-300"
                        style={{ width: `${uploadedFile.progress}%` }}
                      />
                    </div>
                  </div>
                )}

                {/* Error Message */}
                {uploadedFile.status === 'error' && uploadedFile.error && (
                  <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                    {uploadedFile.error}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Summary */}
      {uploadedFiles.length > 0 && (
        <div className="flex items-center justify-between text-sm text-gray-600 bg-gray-50 px-4 py-2 rounded">
          <span>
            {uploadedFiles.length} file{uploadedFiles.length !== 1 ? 's' : ''} •
            {uploadedFiles.filter(f => f.status === 'success').length} uploaded •
            {uploadedFiles.filter(f => f.status === 'error').length} failed
          </span>
          <span>
            {uploadedFiles.filter(f => f.status === 'uploading').length > 0 &&
              `${uploadedFiles.filter(f => f.status === 'uploading').length} uploading...`
            }
          </span>
        </div>
      )}
    </div>
  );
};
