"""
Documents router for CaseBuilder AI.
Handles document upload, processing, and management.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request, UploadFile, File
from fastapi.responses import JSONResponse
import logging
import uuid
import time
from typing import List, Dict, Any
import asyncio

from ..models.responses import (
    DocumentResponse, 
    DocumentListResponse, 
    BaseResponse,
    ProgressResponse
)
from ..services.auth_service import User
from ..dependencies import get_current_user, get_current_session, validate_session_access
from ..middleware.session import session_store
from ..config import get_settings

logger = logging.getLogger(__name__)
router = APIRouter()
settings = get_settings()


@router.post("/upload", response_model=DocumentResponse)
async def upload_document(
    file: UploadFile = File(...),
    request: Request = None,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Upload a document for analysis.
    
    Args:
        file: Uploaded file
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        DocumentResponse with upload details
    """
    try:
        # Validate file type
        if file.content_type not in [
            'application/pdf',
            'image/png',
            'image/jpeg',
            'image/jpg',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type {file.content_type} not supported"
            )
        
        # Validate file size
        file_content = await file.read()
        if len(file_content) > settings.max_file_size:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File size exceeds maximum allowed size of {settings.max_file_size} bytes"
            )
        
        # Generate unique document ID
        document_id = str(uuid.uuid4())
        
        # Get session data
        session_id = getattr(request.state, "session_id")
        session_data = await get_current_session(request)
        
        # Store document in session (volatile storage)
        document_data = {
            "id": document_id,
            "filename": file.filename,
            "content_type": file.content_type,
            "file_size": len(file_content),
            "content": file_content,  # Store in memory for processing
            "uploaded_at": time.time(),
            "processing_status": "uploaded",
            "document_type": None,  # Will be determined by triage
            "metadata": {}
        }
        
        # Update session with document
        documents = session_data.get("documents", {})
        documents[document_id] = document_data
        
        # Update session stats
        stats = session_data.get("stats", {})
        stats["documents_uploaded"] = stats.get("documents_uploaded", 0) + 1
        
        session_store.update_session(session_id, {
            "documents": documents,
            "stats": stats
        })
        
        logger.info(f"Document {file.filename} uploaded by user {user.username}")

        # Start background processing (triage) - temporarily disabled for testing
        # asyncio.create_task(process_document_background(document_id, session_id))
        
        return DocumentResponse(
            document_id=document_id,
            filename=file.filename,
            content_type=file.content_type,
            file_size=len(file_content),
            processing_status="uploaded",
            message="Document uploaded successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading document for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload document"
        )


@router.get("/", response_model=DocumentListResponse)
async def list_documents(
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    List all documents in the current session.
    
    Args:
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        DocumentListResponse with list of documents
    """
    try:
        session_data = await get_current_session(request)
        documents = session_data.get("documents", {})
        
        # Convert to response format
        document_list = []
        for doc_id, doc_data in documents.items():
            document_list.append(DocumentResponse(
                document_id=doc_id,
                filename=doc_data["filename"],
                content_type=doc_data["content_type"],
                file_size=doc_data["file_size"],
                document_type=doc_data.get("document_type"),
                processing_status=doc_data["processing_status"]
            ))
        
        return DocumentListResponse(
            documents=document_list,
            total_count=len(document_list),
            message="Documents retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error listing documents for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve documents"
        )


@router.get("/{document_id}", response_model=DocumentResponse)
async def get_document(
    document_id: str,
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Get document details by ID.
    
    Args:
        document_id: Document identifier
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        DocumentResponse with document details
    """
    try:
        session_data = await get_current_session(request)
        documents = session_data.get("documents", {})
        
        if document_id not in documents:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )
        
        doc_data = documents[document_id]
        
        return DocumentResponse(
            document_id=document_id,
            filename=doc_data["filename"],
            content_type=doc_data["content_type"],
            file_size=doc_data["file_size"],
            document_type=doc_data.get("document_type"),
            processing_status=doc_data["processing_status"],
            message="Document retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting document {document_id} for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve document"
        )


@router.delete("/{document_id}", response_model=BaseResponse)
async def delete_document(
    document_id: str,
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Delete a document from the session.
    
    Args:
        document_id: Document identifier
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        BaseResponse confirming deletion
    """
    try:
        session_id = getattr(request.state, "session_id")
        session_data = await get_current_session(request)
        documents = session_data.get("documents", {})
        
        if document_id not in documents:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )
        
        # Remove document from session
        filename = documents[document_id]["filename"]
        del documents[document_id]
        
        # Update session stats
        stats = session_data.get("stats", {})
        stats["documents_uploaded"] = max(0, stats.get("documents_uploaded", 1) - 1)
        
        session_store.update_session(session_id, {
            "documents": documents,
            "stats": stats
        })
        
        logger.info(f"Document {filename} deleted by user {user.username}")
        
        return BaseResponse(message="Document deleted successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting document {document_id} for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete document"
        )


@router.get("/{document_id}/progress", response_model=ProgressResponse)
async def get_document_progress(
    document_id: str,
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Get document processing progress.
    
    Args:
        document_id: Document identifier
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        ProgressResponse with processing progress
    """
    try:
        session_data = await get_current_session(request)
        documents = session_data.get("documents", {})
        
        if document_id not in documents:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )
        
        doc_data = documents[document_id]
        
        # Determine progress based on status
        progress_map = {
            "uploaded": 10,
            "processing": 50,
            "completed": 100,
            "error": 0
        }
        
        progress = progress_map.get(doc_data["processing_status"], 0)
        
        return ProgressResponse(
            task_id=document_id,
            status=doc_data["processing_status"],
            progress=progress,
            message=f"Document {doc_data['processing_status']}",
            started_at=doc_data["uploaded_at"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting progress for document {document_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get document progress"
        )


async def process_document_background(document_id: str, session_id: str):
    """
    Background task to process uploaded document.
    
    Args:
        document_id: Document identifier
        session_id: Session identifier
    """
    try:
        # Get session data
        session_data = session_store.get_session(session_id)
        if not session_data:
            logger.error(f"Session {session_id} not found for document processing")
            return
        
        documents = session_data.get("documents", {})
        if document_id not in documents:
            logger.error(f"Document {document_id} not found in session")
            return
        
        # Update status to processing
        documents[document_id]["processing_status"] = "processing"
        session_store.update_session(session_id, {"documents": documents})
        
        # TODO: Integrate with existing CaseBuilder logic for document processing
        # This would include OCR, triage, and content extraction
        
        # Simulate processing time
        await asyncio.sleep(2)
        
        # Update status to completed
        documents[document_id]["processing_status"] = "completed"
        documents[document_id]["document_type"] = "unknown"  # Will be determined by triage
        session_store.update_session(session_id, {"documents": documents})
        
        logger.info(f"Document {document_id} processed successfully")
        
    except Exception as e:
        logger.error(f"Error processing document {document_id}: {str(e)}")
        
        # Update status to error
        session_data = session_store.get_session(session_id)
        if session_data:
            documents = session_data.get("documents", {})
            if document_id in documents:
                documents[document_id]["processing_status"] = "error"
                session_store.update_session(session_id, {"documents": documents})
